import idautils
import idaapi
import idc

def get_function_signature(ea):
    func_name = idc.get_func_name(ea)
    args = "/* arguments unknown */"
    return f"void {func_name}({args})"

def get_function_body(ea):
    body = []
    func = idaapi.get_func(ea)
    for head in idautils.Heads(func.start_ea, func.end_ea):
        disasm = idc.GetDisasm(head)
        body.append(f"    // {disasm}")
    return body

def dump_all_functions():
    for func_ea in idautils.Functions():
        signature = get_function_signature(func_ea)
        body = get_function_body(func_ea)

        print(f"{signature} {{")
        for line in body:
            print(line)
        print("}\n")

# Entry point
dump_all_functions()
