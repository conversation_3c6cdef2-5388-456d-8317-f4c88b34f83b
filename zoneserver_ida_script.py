"""
IDAPython script for ZoneServer analysis
Generated by ZoneServer Decompiler
"""

import ida_bytes
import ida_name
import ida_funcs
import ida_struct

def analyze_zoneserver():
    """Main analysis function"""
    print("Starting ZoneServer analysis...")

    # Create important structures
    create_struct("CPlayer")
    create_struct("CMonster")
    create_struct("CGameObject")
    create_struct("CNetWorking")

def create_struct(name):
    """Create a structure in IDA"""
    sid = ida_struct.add_struc(BADADDR, name)
    if sid != BADADDR:
        print(f"Created structure: {name}")
    return sid

if __name__ == "__main__":
    analyze_zoneserver()
