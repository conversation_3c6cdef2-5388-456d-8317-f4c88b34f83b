#!/usr/bin/env python3
"""
Simple ZoneServer Decompiler
Focused on parsing the IDA header file and generating readable C code
"""

import re
import os

class SimpleZoneServerDecompiler:
    def __init__(self, header_file):
        self.header_file = header_file
        self.output_lines = []
        
    def parse_and_generate(self):
        """Parse the header file and generate C code"""
        print(f"Processing {self.header_file}...")
        
        if not os.path.exists(self.header_file):
            print(f"Error: {self.header_file} not found!")
            return False
            
        try:
            with open(self.header_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
        except Exception as e:
            print(f"Error reading file: {e}")
            return False
        
        # Start output
        self.output_lines = [
            "/*",
            " * ZoneServer Decompiled Structures and Enums",
            " * Generated from IDA header file: " + self.header_file,
            " * This file contains the main game structures and enumerations",
            " */",
            "",
            "#ifndef ZONESERVER_DECOMPILED_H",
            "#define ZONESERVER_DECOMPILED_H",
            "",
            "#include <stdint.h>",
            "#include <windows.h>",
            "",
            "// Basic type definitions from IDA",
            "#define __int8 char",
            "#define __int16 short", 
            "#define __int32 int",
            "#define __int64 long long",
            ""
        ]
        
        # Parse and add content
        self._parse_structs(content)
        self._parse_enums(content)
        
        # End output
        self.output_lines.extend([
            "",
            "#endif // ZONESERVER_DECOMPILED_H"
        ])
        
        return True
    
    def _parse_structs(self, content):
        """Parse struct declarations"""
        self.output_lines.append("// ===== STRUCTURE DECLARATIONS =====")
        self.output_lines.append("")
        
        # Find struct forward declarations
        struct_pattern = r'struct\s+([^;]+);'
        structs_found = []
        
        for match in re.finditer(struct_pattern, content):
            struct_name = match.group(1).strip()
            if struct_name not in structs_found:
                structs_found.append(struct_name)
        
        # Categorize and output structs
        categories = {
            'Game Core': [],
            'Network': [],
            'Database': [],
            'Graphics (DirectX)': [],
            'AI & Pathfinding': [],
            'Guild System': [],
            'Combat & Skills': [],
            'Crypto & Security': [],
            'Windows/MFC': [],
            'Other': []
        }
        
        for struct in structs_found:
            name_lower = struct.lower()
            if any(x in name_lower for x in ['player', 'monster', 'gameobject', 'entity', 'level', 'map']):
                categories['Game Core'].append(struct)
            elif any(x in name_lower for x in ['net', 'socket', 'msg', 'packet', 'network']):
                categories['Network'].append(struct)
            elif any(x in name_lower for x in ['db', 'database', 'query', 'record']):
                categories['Database'].append(struct)
            elif any(x in name_lower for x in ['d3d', 'vertex', 'texture', 'render', 'directx']):
                categories['Graphics (DirectX)'].append(struct)
            elif any(x in name_lower for x in ['ai', 'path', 'aggro']):
                categories['AI & Pathfinding'].append(struct)
            elif any(x in name_lower for x in ['guild', 'party']):
                categories['Guild System'].append(struct)
            elif any(x in name_lower for x in ['attack', 'damage', 'skill', 'combat', 'weapon']):
                categories['Combat & Skills'].append(struct)
            elif any(x in name_lower for x in ['crypto', 'crypt', 'hash', 'security', 'hackshield']):
                categories['Crypto & Security'].append(struct)
            elif any(x in name_lower for x in ['cwnd', 'mfc', 'afx', 'ole', 'com']):
                categories['Windows/MFC'].append(struct)
            else:
                categories['Other'].append(struct)
        
        # Output categorized structs
        for category, struct_list in categories.items():
            if struct_list:
                self.output_lines.append(f"// {category} Structures ({len(struct_list)} items)")
                for struct in sorted(struct_list):
                    if '_vtbl' in struct:
                        self.output_lines.append(f"struct {struct}; // Virtual table")
                    else:
                        self.output_lines.append(f"struct {struct};")
                self.output_lines.append("")
    
    def _parse_enums(self, content):
        """Parse enum definitions"""
        self.output_lines.append("// ===== ENUMERATIONS =====")
        self.output_lines.append("")
        
        # Pattern to match enum definitions
        enum_pattern = r'/\* \d+ \*/\s*enum\s+([^:]+)\s*:\s*(\w+)\s*\{([^}]+)\}'
        
        game_enums = []
        system_enums = []
        
        for match in re.finditer(enum_pattern, content, re.DOTALL):
            enum_name = match.group(1).strip()
            base_type = match.group(2).strip()
            enum_body = match.group(3).strip()
            
            # Clean up enum name (remove $ prefixes for generated names)
            if enum_name.startswith('$'):
                continue  # Skip auto-generated enum names
            
            # Parse enum values
            values = []
            for line in enum_body.split(','):
                line = line.strip()
                if '=' in line and line:
                    name_part, value_part = line.split('=', 1)
                    name = name_part.strip()
                    value_str = value_part.strip()
                    values.append((name, value_str))
            
            if values:  # Only add enums with values
                enum_info = (enum_name, base_type, values)
                
                # Categorize enums
                name_lower = enum_name.lower()
                if any(x in name_lower for x in ['game', 'player', 'monster', 'skill', 'combat', 'quest', 'guild']):
                    game_enums.append(enum_info)
                else:
                    system_enums.append(enum_info)
        
        # Output game-related enums first
        if game_enums:
            self.output_lines.append("// Game-Related Enumerations")
            for enum_name, base_type, values in game_enums:
                self._write_enum(enum_name, base_type, values)
            self.output_lines.append("")
        
        # Output system enums
        if system_enums:
            self.output_lines.append("// System Enumerations")
            for enum_name, base_type, values in system_enums[:20]:  # Limit to first 20
                self._write_enum(enum_name, base_type, values)
    
    def _write_enum(self, name, base_type, values):
        """Write a single enum definition"""
        self.output_lines.append(f"enum {name} : {base_type} {{")
        for value_name, value_str in values:
            self.output_lines.append(f"    {value_name} = {value_str},")
        self.output_lines.append("};")
        self.output_lines.append("")
    
    def save_output(self, output_file="zoneserver_simple.h"):
        """Save the generated code to a file"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(self.output_lines))
            print(f"✅ Generated: {output_file}")
            return True
        except Exception as e:
            print(f"❌ Error saving {output_file}: {e}")
            return False
    
    def generate_summary(self, summary_file="zoneserver_summary.txt"):
        """Generate a summary report"""
        try:
            with open(summary_file, 'w', encoding='utf-8') as f:
                f.write("ZoneServer Decompilation Summary\n")
                f.write("=" * 40 + "\n\n")
                f.write(f"Source file: {self.header_file}\n")
                f.write(f"Generated lines: {len(self.output_lines)}\n\n")
                
                # Count structures and enums
                struct_count = sum(1 for line in self.output_lines if line.startswith('struct '))
                enum_count = sum(1 for line in self.output_lines if line.startswith('enum '))
                
                f.write(f"Structures found: {struct_count}\n")
                f.write(f"Enumerations found: {enum_count}\n\n")
                
                f.write("Key Game Structures Identified:\n")
                f.write("- Player management systems\n")
                f.write("- Monster and NPC systems\n") 
                f.write("- Network communication\n")
                f.write("- Database interfaces\n")
                f.write("- Graphics rendering (DirectX)\n")
                f.write("- Guild and party systems\n")
                f.write("- Combat and skill systems\n")
                f.write("- Security systems (HackShield)\n")
                
            print(f"✅ Generated: {summary_file}")
            return True
        except Exception as e:
            print(f"❌ Error saving {summary_file}: {e}")
            return False

def main():
    header_file = "ZoneServerUD_x64.exe.h"
    
    print("🚀 Simple ZoneServer Decompiler")
    print("=" * 40)
    
    decompiler = SimpleZoneServerDecompiler(header_file)
    
    if decompiler.parse_and_generate():
        decompiler.save_output("zoneserver_decompiled.h")
        decompiler.generate_summary("zoneserver_summary.txt")
        print("\n✅ Decompilation completed successfully!")
        print("\nGenerated files:")
        print("  📄 zoneserver_decompiled.h - Main header with structures and enums")
        print("  📊 zoneserver_summary.txt  - Summary report")
    else:
        print("\n❌ Decompilation failed!")

if __name__ == "__main__":
    main()
