import os
import hashlib
import re
import idautils
import idaapi
import idc

# --- DEMANGLING SUPPORT ---
try:
    import ida_demangle
    MNG_NODE = ida_demangle.MNG_NODE
    demangling_supported = True
except ImportError:
    demangling_supported = False
    MNG_NODE = None

# --- CONFIG ---
output_root = "D:\\4_GameGuardProject2232server\\Decompiled"
max_name_len = 128

# --- UTILITIES ---
def get_namespace(func_name):
    if "allocator" in func_name or "vector" in func_name:
        return "STL_Container"
    return func_name.split("_")[0] if "_" in func_name else "Global"

def ensure_directory(path):
    try:
        os.makedirs(path, exist_ok=True)
    except Exception as e:
        print(f"[!] Failed to create directory: {path}\n{e}")

def sanitize_name(name):
    invalid_chars = '<>:"/\\|?*'
    safe = ''.join(c if c not in invalid_chars else '_' for c in name)
    safe = re.sub(r'[@$]+', '_', safe)
    return safe

def shorten_if_needed(name):
    if len(name) > max_name_len:
        hashed = hashlib.md5(name.encode()).hexdigest()[:8]
        return name[:40] + "_" + hashed
    return name

def safe_name(name):
    return shorten_if_needed(sanitize_name(name))

def get_function_signature(ea):
    raw_name = idc.get_func_name(ea)
    demangled = idc.demangle_name(raw_name, MNG_NODE) if demangling_supported else None
    func_name = safe_name(demangled if demangled else raw_name)
    args = "/* unknown */"
    return func_name, f"void {func_name}({args});"

def get_function_body(ea):
    body = []
    func = idaapi.get_func(ea)
    if func:
        for head in idautils.Heads(func.start_ea, func.end_ea):
            disasm = idc.GetDisasm(head)
            body.append(f"    // {disasm}")
    return body

def write_function_files(namespace, func_name_raw, signature, body_lines):
    namespace_safe = safe_name(namespace)
    func_name_safe = safe_name(func_name_raw)

    ns_folder = os.path.join(output_root, namespace_safe)
    ensure_directory(ns_folder)

    if not os.path.exists(ns_folder):
        print(f"[!] Skipping '{func_name_raw}' — folder '{ns_folder}' could not be created.")
        return

    header_path = os.path.join(ns_folder, f"{func_name_safe}.h")
    cpp_path = os.path.join(ns_folder, f"{func_name_safe}.cpp")

    try:
        with open(header_path, "w", encoding="utf-8") as h_file:
            h_file.write(f"#pragma once\n\n{signature}\n")

        with open(cpp_path, "w", encoding="utf-8") as cpp_file:
            cpp_file.write(f'#include "{func_name_safe}.h"\n\n{signature.replace(";", "")} {{\n')
            for line in body_lines:
                cpp_file.write(f"{line}\n")
            cpp_file.write("}\n")

        print(f"[+] Exported: {cpp_path}")
    except Exception as e:
        print(f"[!] Failed to write files for '{func_name_raw}': {e}")

def process_functions():
    for func_ea in idautils.Functions():
        raw_name = idc.get_func_name(func_ea)
        demangled = idc.demangle_name(raw_name, MNG_NODE) if demangling_supported else None
        display_name = demangled if demangled else raw_name

        namespace = get_namespace(display_name)
        func_name, signature = get_function_signature(func_ea)
        body = get_function_body(func_ea)
        write_function_files(namespace, display_name, signature, body)

# --- RUN ---
process_functions()
