[total_schedule_count]
schedule_count = 3

[Schedule_0]
HSS_SCENE_CHANGE_TERM = 600000
HSS_SCENE_BATTLE_TERM = 7200000
HSS_SCENE_ATTACKABLE_KEEPER_TERM = 1800000

HSS_SCENE_DESATTACKABLE_KEEPER_TERM = 10800000
HSS_SCENE_CHAOS_KEEPER_TERM = 3000000
HSS_SCENE_BASE_MINIE_TERM = 10800000
HSS_SCENE_TOUCH_DOWN_ADD_TERM = 3600000
[Schedule_1]
HSS_SCENE_CHANGE_TERM = 600000
HSS_SCENE_BATTLE_TERM = 7200000
HSS_SCENE_ATTACKABLE_KEEPER_TERM = 1800000

HSS_SCENE_DESATTACKABLE_KEEPER_TERM = 10800000
HSS_SCENE_CHAOS_KEEPER_TERM = 3000000
HSS_SCENE_BASE_MINIE_TERM = 10800000
HSS_SCENE_TOUCH_DOWN_ADD_TERM = 3600000
[Schedule_2]
HSS_SCENE_CHANGE_TERM = 600000
HSS_SCENE_BATTLE_TERM = 7200000
HSS_SCENE_ATTACKABLE_KEEPER_TERM = 1800000

HSS_SCENE_DESATTACKABLE_KEEPER_TERM = 10800000
HSS_SCENE_CHAOS_KEEPER_TERM = 3000000
HSS_SCENE_BASE_MINIE_TERM = 10800000
HSS_SCENE_TOUCH_DOWN_ADD_TERM = 3600000
[HolyWarFreeMining]
FreeMining = FALSE

[HolySystem]
MentalPass = TRUE
KeeperCreateMap = resources
KeeperCreateDummy = dk0001
KeeperActiveDummy = hskactdum
KeeperCenterDummy = dk0001
KeeperCallCode = xxxux7xx1xxvxxx
KeeperMonsterCode = 04903

FirstKeeperHPRate=5
KeeperHPRate = 20

HolyMental = iyhol01
;HolyMental = iyyyy01

StoneNum = 3

StoneCreateMap0 = resources
StoneCreateDummy0 = dh001
StoneMonsterCode0 = 04B00
StoneMasterRace0 = 0

StoneCreateMap1 = resources
StoneCreateDummy1 = dh002
StoneMonsterCode1 = 04B01
StoneMasterRace1 = 1

StoneCreateMap2 = resources
StoneCreateDummy2 = dh003
StoneMonsterCode2 = 04B02
StoneMasterRace2 = 2
[RaceBuff]
thCnt = 3
WinGetScaner0 = AF1F3 1
WinEmptyScaner0 = AF1F4 1
Fail0 = AF1F5 1
Lose0 = AF1F6 1

WinGetScaner1 = AF1F3 2
WinEmptyScaner1 = AF1F4 2
Fail1 = AF1F5 2
Lose1 = AF1F6 2

WinGetScaner2 = AF1F3 3
WinEmptyScaner2 = AF1F4 3
Fail2 = AF1F5 3
Lose2 = AF1F6 3

[RaceBattleRewardItem]
ItemDropCntOnce = 50
ItemDropDelay = 500

FixCnt = 50
Fix0 = irtal01 1 1
Fix1 = irtal02 1 1
Fix2 = irtal03 1 1
Fix3 = irtal04 1 1
Fix4 = irtal05 1 1
Fix5 = irtal06 1 1
Fix6 = irtal07 1 1
Fix7 = irtal08 1 1
Fix8 = irtal09 1 1
Fix9 = irtal10 1 1
Fix10 = irtal11 1 1
Fix11 = irtal12 1 1
Fix12 = irtal13 1 1
Fix13 = irtal14 1 1
Fix14 = iibba65 0 1
Fix15 = iacca65 0 1
Fix16 = iaaab43 0 1
Fix17 = ipacu00 1 40
Fix18 = ipbcu00 1 40
Fix19 = ipccu00 1 40
Fix20 = ipbco01 1 40
Fix21 = ipcco01 1 40
Fix22 = ipaco01 1 40
Fix23 = itttt03 0 40
Fix24 = iwswb40 0 1
Fix25 = iwstb40 0 1
Fix26 = iwflb40 0 1
Fix27 = iwfib40 0 1
Fix28 = iwspb40 0 1
Fix29 = iwswb35 0 1
Fix30 = iwstb35 0 1
Fix31 = iwflb35 0 1
Fix32 = iwfib35 0 1
Fix33 = iwspb35 0 1
Fix34 = irgef03 1 1
Fix35 = irgea03 1 1
Fix36 = irget03 1 1
Fix37 = irgew03 1 1
Fix38 = irgef02 1 2
Fix39 = irgea02 1 2
Fix40 = irget02 1 2
Fix41 = irgew02 1 2
Fix42 = irgef01 1 4
Fix43 = irgea01 1 4
Fix44 = irget01 1 4
Fix45 = irgew01 1 4
Fix46 = iyyyy02 99 10
Fix47 = iyyyy03 99 5
Fix48 = iyyyy21 99 10
Fix49 = iyyyy22 99 5

RateCnt = 5
Rate0 = ioblu03 9 10000
Rate1 = iored03 9 2000
Rate2 = ioyel03 9 2000
Rate3 = iogre03 9 2000
Rate4 = iobla03 9 2000


[PortalDummyName]
BellaDummyName	= dpfrom_bellato_portal
CoraDummyName	= dpfrom_cora_portal
AccDummyName	= dpfrom_accretia_portal

[RaceBattlePoint]
WinGetScanet    = 5000
WinEmptyScaner = 0
FailGetScaner	= 3000
FailEmptyScaner = 0
LoseGetScaner  = 1000
LoseEmptyScaner = 0
[OreTotalAmount]
Set = 1
Minset = 20
SAT_3 = 500
