[Field]
mapcnt = 3

[UseMap]
Bellato = 0
Cora = 1
Accratia = 2

[Map0]
Name = NeutralB

1PStartPosDummyName = bd_sbnr01
2PStartPosDummyName = bd_sbnb01

1PGoalPosCnt = 1
1PGoalPosDummyName0 = dp_zbnr01

2PGoalPosCnt = 1
2PGoalPosDummyName0 = dp_zbnb01

BallRegenPosCnt = 3
BallRegenDummyName0 = dp_gbnh01
BallRegenDummyName1 = dp_gbnh02
BallRegenDummyName2 = dp_gbnh03

[Map1]
Name = NeutralC

1PStartPosDummyName = bd_scnr01
2PStartPosDummyName = bd_scnb01

1PGoalPosCnt = 1
1PGoalPosDummyName0 = dp_zcnr01

2PGoalPosCnt = 1
2PGoalPosDummyName0 = dp_zcnb01

BallRegenPosCnt = 3
BallRegenDummyName0 = dp_gcnh01
BallRegenDummyName1 = dp_gcnh02
BallRegenDummyName2 = dp_gcnh03


[Map2]
Name = NeutralA

1PStartPosDummyName = bd_sanr01
2PStartPosDummyName = bd_sanb01

1PGoalPosCnt = 1
1PGoalPosDummyName0 = dp_zanr01

2PGoalPosCnt = 1
2PGoalPosDummyName0 = dp_zanb01

BallRegenPosCnt = 3
BallRegenDummyName0 = dp_ganh01
BallRegenDummyName1 = dp_ganh02
BallRegenDummyName2 = dp_ganh03

[RewardItem]
itemCnt = 1
item0 = iyscr01 1