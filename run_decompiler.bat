@echo off
echo Starting ZoneServer Decompiler...
echo.

REM Try different Python commands
python run_decompiler.py 2>nul
if %errorlevel% equ 0 goto :success

python3 run_decompiler.py 2>nul
if %errorlevel% equ 0 goto :success

py run_decompiler.py 2>nul
if %errorlevel% equ 0 goto :success

echo Python not found or script failed to run.
echo Please ensure Python is installed and try running manually:
echo   python run_decompiler.py
echo.
pause
goto :end

:success
echo.
echo Decompilation completed successfully!
pause

:end
