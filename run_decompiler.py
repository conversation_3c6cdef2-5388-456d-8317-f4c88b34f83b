#!/usr/bin/env python3
"""
Simple script to run the ZoneServer decompiler
"""

from zoneserver_decompiler import ZoneServerDecompiler
import os

def main():
    header_file = "ZoneServerUD_x64.exe.h"
    
    if not os.path.exists(header_file):
        print(f"Error: {header_file} not found in current directory")
        return
    
    print("Starting ZoneServer decompilation...")
    
    # Create decompiler instance
    decompiler = ZoneServerDecompiler(header_file)
    
    # Parse the header file
    decompiler.parse_header()
    
    # Generate outputs
    decompiler.generate_c_code("zoneserver_decompiled.c")
    decompiler.generate_analysis_report("zoneserver_analysis.txt")
    decompiler.extract_function_signatures("zoneserver_functions.h")
    decompiler.generate_ida_script("zoneserver_ida_script.py")
    
    print("\nDecompilation completed successfully!")
    print("\nGenerated files:")
    print("  📄 zoneserver_decompiled.c     - Main C code with structures and enums")
    print("  📊 zoneserver_analysis.txt     - Analysis report categorizing structures")
    print("  🔧 zoneserver_functions.h      - Function signatures from vtables")
    print("  🐍 zoneserver_ida_script.py    - IDAPython script for further analysis")
    
    # Show some quick stats
    print(f"\nQuick Statistics:")
    print(f"  Structures found: {len(decompiler.structs)}")
    print(f"  Enumerations found: {len(decompiler.enums)}")
    print(f"  Type definitions found: {len(decompiler.typedefs)}")
    print(f"  Defines found: {len(decompiler.defines)}")

if __name__ == "__main__":
    main()
