# ZoneServer Decompilation Analysis

## Overview
This analysis is based on the IDA-generated header file `ZoneServerUD_x64.exe.h` which contains type definitions extracted from the ZoneServer executable. The server appears to be for an MMORPG with complex game systems.

## Key Game Systems Identified

### 1. **Character System**
- **Classes/Roles**: Warrior, Ranger, Healer, Force (magic user)
- **Animus System**: Character progression/class system
- **Player States**: Chaos mode, PVP states, various timers
- **Storage System**: Multiple storage types (inventory, equipment, trunk, etc.)

### 2. **Combat & Skills**
- **Elemental System**: Fire, Water, Earth, Wind elements
- **Skill System**: Class-based skills with elemental affinities
- **Weapon System**: Bullet/projectile linking for ranged weapons
- **Damage System**: Continuous damage, stun effects, aggro management

### 3. **PVP (Player vs Player)**
- **PVP Money System**: Kill rewards, scanner usage, shop purchases
- **Ranking System**: Race-based and guild-based rankings
- **Punishment System**: Chat restrictions, chaos penalties, party restrictions
- **Battle States**: Various PVP battle phases and timers

### 4. **Guild System**
- **Guild Management**: Member management, applications, rankings
- **Guild Battles**: Structured guild vs guild combat
- **Leadership Roles**: Patriarch, sub-patriarchs, attack/defense/support leaders
- **Guild Resources**: Money management, member limits

### 5. **Quest System**
- **Quest Types**: Individual and party quests
- **Quest Triggers**: NPC interaction, PK events, level requirements, item requirements
- **Quest Management**: Timeout handling, completion tracking

### 6. **Economy & Items**
- **Cash Shop**: Real money transactions with various error codes
- **Item Combination**: Crafting system with success probabilities
- **Item Exchange**: Trading system between players
- **Loot System**: Drop tables, random generation
- **Storage Management**: Multiple storage containers per player

### 7. **Mining & Resources**
- **Automated Mining**: Personal mining units with battery systems
- **Ore Management**: Different ore types, cutting, processing
- **Resource Control**: Ownership changes, work states

### 8. **Security Systems**
- **HackShield Integration**: Anti-cheat system with verification states
- **Speed Hack Detection**: Movement validation
- **Client Verification**: CRC checks, GUID validation
- **Kick Reasons**: Various security violation types

### 9. **Holy Stone System**
- **Battle Phases**: Timed battle sequences
- **Keeper System**: Special NPCs with attack/defense phases
- **Scene States**: Complex state machine for holy stone events

### 10. **Network & Database**
- **Database Operations**: User data, rankings, guild information
- **Network Management**: Socket handling, message processing
- **Synchronization**: Client-server state synchronization

## Technical Architecture

### Core Components
- **CMainThread**: Main game loop and processing
- **CNetWorking**: Network communication layer
- **CUserDB**: Player data persistence
- **CMapData**: World/level management
- **CFrameRate**: Performance management

### AI Systems
- **CMonsterAI**: NPC behavior and decision making
- **CPathMgr**: Pathfinding for NPCs and players
- **AggroCaculateData**: Threat/aggro calculation system

### Graphics Integration
- **DirectX Integration**: D3D structures for 3D rendering
- **Collision Detection**: Line drawing and collision systems
- **Visual Effects**: Particle systems, animations

## Security Measures
The server implements multiple layers of security:
1. **Client Verification**: HackShield integration
2. **Movement Validation**: Speed hack detection
3. **Data Integrity**: CRC checks and checksums
4. **Session Management**: User authentication and session tracking

## Database Schema Insights
Based on the enums and structures, the database likely contains:
- **User Tables**: Character data, statistics, inventory
- **Guild Tables**: Guild information, member lists, rankings
- **Item Tables**: Item definitions, properties, combinations
- **Quest Tables**: Quest definitions, progress tracking
- **PVP Tables**: Rankings, battle history, rewards
- **Cash Shop Tables**: Transaction logs, item purchases

## Scripting System
- **Lua Integration**: CLuaCommand and related structures suggest Lua scripting
- **Event System**: Lua-based event handling for game mechanics
- **Dynamic Content**: Server-side scripting for quests and events

## Recommendations for Further Analysis

1. **Function Analysis**: Use IDA to analyze the actual function implementations
2. **Memory Layout**: Examine structure member layouts and sizes
3. **Network Protocol**: Analyze message structures and communication patterns
4. **Database Schema**: Reverse engineer the actual database structure
5. **Security Assessment**: Evaluate the effectiveness of anti-cheat measures

## Files Generated
- `zoneserver_decompiled.h` - Main header with game enums and structure declarations
- `zoneserver_analysis.md` - This analysis document
- `simple_decompiler.py` - Python script for parsing IDA headers
- `run_decompiler.py` - Execution script for the decompiler

This analysis provides a foundation for understanding the ZoneServer architecture and can be used for further reverse engineering, security analysis, or game development research.
