/*
 * ZoneServer Decompiled Header
 * Generated from IDA header file: ZoneServerUD_x64.exe.h
 * 
 * This file contains the main game structures and enumerations
 * extracted from the ZoneServer executable.
 */

#ifndef ZONESERVER_DECOMPILED_H
#define ZONESERVER_DECOMPILED_H

#include <stdint.h>
#include <windows.h>

// Basic type definitions from IDA
#define __int8 char
#define __int16 short
#define __int32 int
#define __int64 long long

// ===== GAME-RELATED ENUMERATIONS =====

// Mining/Resource System
enum amine_sub_qry : __int32 {
    amine_sub_qry_changeowner = 0x0,
    amine_sub_qry_batterycharge = 0x1,
    amine_sub_qry_mineore = 0x2,
    amine_sub_qry_workstate = 0x3,
    amine_sub_qry_selore = 0x4,
    amine_sub_qry_battery_discharge = 0x5,
    amine_sub_qry_moveore = 0x6,
};

// Event System
enum EVENT_TIME : __int32 {
    Time_begin = 0x0,
    Time_end = 0x1,
    Time_all = 0x2,
};

// Monster AI System
enum MON_AI_KIND : __int32 {
    MON_AI_DEFALUT = 0x0,
    MON_AI_MAX = 0x1,
};

// Potion System
enum _POTION_DELAY_GROUP : __int32 {
    eHP = 0x0,
    eFP = 0x1,
    eSP = 0x2,
    eCURE = 0x3,
    eONE_CONT = 0x4,
    eTWO_CONT = 0x5,
    eTHREE_CONT = 0x6,
    eFOUR_CONT = 0x7,
    eCHOSE = 0x8,
    eCHOSE_CURE = 0x9,
    eADRENALIN = 0xA,
    eDEFENCE = 0xB,
    eEVENT = 0xC,
    eClassRefine = 0xD,
    eAllRemove_CtDm = 0xE,
    eCharCall = 0xF,
    eTeleport = 0x10,
    eRENAME = 0x11,
    eEXP_INCREASE = 0x12,
    eRune = 0x13,
    eExpRate = 0x14,
    eMasteryRate = 0x15,
    eItemDropRate = 0x16,
    eMineSpeedRate = 0x17,
    eRevivePotion = 0x18,
    eBufExtPotion = 0x19,
    eTrunkExtendPotion = 0x1A,
    eRaceBuffClearPotion = 0x1B,
    eGoldCapsulePotion = 0x1C,
    eRecallAfterStone = 0x21,
    eTeleportAfterStone = 0x22,
    eInvisibleState = 0x23,
    eHFSFullRecover = 0x24,
    eContDamageRemove = 0x25,
    eMax_PotionClass = 0x26,
};

// PVP System
enum PVP_MONEY_ALTER_TYPE : __int32 {
    pm_kill = 0x0,
    pm_reward = 0x1,
    pm_scaner = 0x2,
    pm_shop = 0x3,
    pm_quest = 0x4,
};

enum PVP_ALTER_TYPE : __int32 {
    kill_s_inc = 0x0,
    kill_p_inc = 0x1,
    die_dec = 0x2,
    quest_inc = 0x3,
};

// Punishment System
enum PATRIARCH_PUNISHMENT_TYPE : __int32 {
    PUNISHMENT_TYPE_CHAT = 0x0,
    PUNISHMENT_TYPE_CHAOS = 0x1,
    PUNISHMENT_TYPE_PARTY = 0x2,
    PUNISHMENT_TYPE_NUM = 0x3,
};

// Character Classes/Roles
enum CAnimus : __int32 {
    role_etc = 0x0,
    role_warrior = 0x1,
    role_ranger = 0x2,
    role_healer = 0x3,
    role_force = 0x4,
};

// Skill Elements
enum SKILL : __int32 {
    FIREEL = 0x0,
    WATEREL = 0x1,
    SOILEL = 0x2,
    WINDEL = 0x3,
    NONE = 0xFFFFFFFF,
};

// Storage System
enum _STORAGE_POS : __int32 {
    INVEN = 0x0,
    EQUIP = 0x1,
    EMBELLISH = 0x2,
    FORCE = 0x3,
    ANIMUS = 0x4,
    TRUNK = 0x5,
    PERSONAL_AMINE = 0x6,
    EXT_TRUNK = 0x7,
    STORAGE_NUM = 0x8,
};

// Quest System
enum QUEST_HAPPEN : __int32 {
    quest_happen_type_dummy = 0x0,
    quest_happen_type_npc = 0x1,
    quest_happen_type_pk = 0x2,
    quest_happen_type_lv = 0x3,
    quest_happen_type_class = 0x4,
    quest_happen_type_grade = 0x5,
    quest_happen_type_item = 0x6,
    quest_happen_type_mastery = 0x7,
    quest_happen_type_maxlevel = 0x8,
    QUEST_HAPPEN_TYPE_NUM = 0x9,
};

// Cash Shop System
enum CS_RCODE : __int32 {
    CsSucceed = 0x0,
    CsWrongQuerySentence = 0x1,
    CsWrongUserID = 0x2,
    CsWrongGameCode = 0x3,
    CsWrongItemStringCode = 0x4,
    CsWrongPrice = 0x5,
    CsWrongOverlapNum = 0x6,
    CsWrongDiscount = 0x7,
    CsWrongUID = 0x8,
    CsWrongServerName = 0x9,
    CsWrongAvatorName = 0xA,
    CsWrongStoreIndex = 0xB,
    CsUnfitForUse = 0xC,
    CsNotEnoughCashAmount = 0xD,
    CsRollbackFailed = 0xE,
    CsItemInsertToInvenFailed = 0xF,
    CsEmptySlotLack = 0x10,
    CsNonmatchCashAmountLocalAndDb = 0x11,
    CsWrongEvent = 0x12,
    CsNotUseCouponType = 0x13,
    CsNoUseCouponWithEvent = 0x14,
    CsNotEnoughLimitedSaleNum = 0x15,
    CsNotEventTime = 0x16,
};

// Security System (HackShield)
enum HACKSHEILD_PARAM_ANTICP_KICK_REASON : __int32 {
    KICK_REASON_CRC_ACK_DELAY = 0x1,
    KICK_REASON_FIRST_GUID_INVALID = 0x2,
    KICK_REASON_CRC_ACK_INVALID = 0x3,
    KICK_REASON_NOT_HACKSHEILD_CLIENT = 0x4,
};

enum HACKSHIELD_VERIFY_STATE : __int32 {
    VERIFY_NONE = 0x0,
    VERIFY_FIRST_CHECK_REQUEST = 0x1,
    VERIFY_FIRST_CHECK_COMPLETE = 0x2,
    VERIFY_CHECK_REQUEST = 0x3,
    VERIFY_CHECK_COMPLETE = 0x4,
};

// Holy Stone System
enum HSS_SCENE_STATE : __int32 {
    HS_SCENE_INIT = 0x0,
    HS_SCENE_BATTLE_TIME = 0x1,
    HS_SCENE_BATTLE_END_WAIT_TIME = 0x2,
    HS_SCENE_KEEPER_ATTACKABLE_TIME = 0x3,
    HS_SCENE_KEEPER_DEATTACKABLE_TIME = 0x4,
    HS_SCENE_KEEPER_DIE_TIME = 0x5,
    HS_SCENE_KEEPER_CHAOS_TIME = 0x6,
    HS_SCENE_STATE_MAX = 0x7,
};

enum HSS_CHECK_TIMER : __int32 {
    HSCT_CRI_BATTLE_START = 0x0,
    HSCT_CRI_BATTLE_END = 0x1,
    HSCT_KEEPER_START_ATTACKABLE = 0x2,
    HSCT_KEEPER_START_DEATTACKABLE = 0x3,
    HSCT_KEEPER_START_DIE = 0x4,
    HSCT_KEEPER_START_CHAOS = 0x5,
    HSCT_KEEPER_END = 0x6,
    HSCT_MAX = 0x7,
};

// Player System
enum CPlayer_CHAOS_MODE_STATE : __int32 {
    chaos_mode_remain_time_0 = 0x0,
    chaos_mode_remain_time_10per_up = 0x1,
    chaos_mode_remain_time_10per_down = 0x2,
};

enum CPlayer_PVP_LOSE : __int32 {
    race_battle = 0x0,
    no_connect_long_time = 0x1,
};

// Monster System
enum CMonster_MonsterEvent : __int32 {
    eEvent_Damage = 0x0,
    eEvent_Help = 0x1,
    eEvent_MAx = 0x2,
};

// AI System
enum AI_MGR_TYPE : __int32 {
    AI_MGR_DF = 0x0,
    AI_MGR_MAX = 0x1,
};

// ===== STRUCTURE DECLARATIONS =====

// Forward declarations of main game structures
struct CPlayer;                    // Player character
struct CMonster;                   // Monster/NPC entities  
struct CGameObject;                // Base game object
struct CMapData;                   // Map/level data
struct CLevel;                     // Level management
struct CEntity;                    // Base entity
struct CAnimus;                    // Character class system
struct CGuild;                     // Guild system
struct CNetWorking;                // Network management
struct CUserDB;                    // User database
struct CMainThread;                // Main game thread
struct CFrameRate;                 // Frame rate management
struct CItemBox;                   // Item containers
struct CQuestMgr;                  // Quest management
struct CHolyStoneSystem;           // Holy stone system
struct CHackShieldExSystem;        // Anti-cheat system
struct CPathMgr;                   // Pathfinding
struct CMonsterAI;                 // Monster AI
struct CCollLineDraw;              // Collision detection
struct CDarkHole;                  // Dark hole system
struct CWeaponBulletLinkTable;     // Weapon systems
struct CLuaCommand;                // Lua scripting
struct CRFNewDatabase;             // Database system

// Virtual table structures (for reverse engineering)
struct CPlayer_vtbl;
struct CMonster_vtbl;
struct CGameObject_vtbl;
struct CMapData_vtbl;
struct CNetWorking_vtbl;
struct CUserDB_vtbl;

#endif // ZONESERVER_DECOMPILED_H
