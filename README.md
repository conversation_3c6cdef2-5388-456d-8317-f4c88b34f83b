# ZoneServer Decompiler

A Python-based tool for parsing IDA-generated header files and converting them into readable C-like code structures.

## Overview

This project provides tools to analyze and decompile the ZoneServer executable header file (`ZoneServerUD_x64.exe.h`) generated by IDA Pro. The tools extract game structures, enumerations, and provide insights into the server architecture.

## Files Included

### Generated Output Files
- **`zoneserver_decompiled.h`** - Main header file with cleaned up enums and structure declarations
- **`zoneserver_analysis.md`** - Detailed analysis of the game systems and architecture
- **`README.md`** - This documentation file

### Python Tools
- **`simple_decompiler.py`** - Lightweight decompiler focused on parsing IDA headers
- **`zoneserver_decompiler.py`** - Full-featured decompiler with advanced analysis
- **`run_decompiler.py`** - Simple execution script
- **`run_decompiler.bat`** - Windows batch file for easy execution

## Quick Start

### Method 1: Use Pre-generated Files
The easiest way is to use the already generated files:
- Open `zoneserver_decompiled.h` for the main C header
- Read `zoneserver_analysis.md` for detailed analysis

### Method 2: Run the Decompiler
If you want to run the decompiler yourself:

```bash
# Make sure you have Python installed
python simple_decompiler.py
```

Or on Windows, double-click:
```
run_decompiler.bat
```

## What's Included in the Analysis

### Game Systems Identified
- **Character System**: Classes (Warrior, Ranger, Healer, Force), progression
- **Combat System**: Elemental magic, weapons, skills, aggro management
- **PVP System**: Player vs player combat, rankings, rewards
- **Guild System**: Guild management, battles, leadership roles
- **Quest System**: Individual and party quests, triggers, management
- **Economy**: Cash shop, item crafting, trading, loot systems
- **Mining System**: Automated resource gathering
- **Security**: Anti-cheat (HackShield), speed hack detection
- **Holy Stone System**: Special PVP/event system

### Technical Components
- **Network Layer**: Socket management, message processing
- **Database Layer**: User data, rankings, persistence
- **AI Systems**: Monster AI, pathfinding
- **Graphics**: DirectX integration, collision detection
- **Scripting**: Lua integration for dynamic content

## Key Enumerations Extracted

```c
// Character Classes
enum CAnimus : __int32 {
    role_warrior = 0x1,
    role_ranger = 0x2,
    role_healer = 0x3,
    role_force = 0x4,
};

// Storage Types
enum _STORAGE_POS : __int32 {
    INVEN = 0x0,      // Inventory
    EQUIP = 0x1,      // Equipment
    TRUNK = 0x5,      // Storage trunk
    // ... more
};

// PVP Money Sources
enum PVP_MONEY_ALTER_TYPE : __int32 {
    pm_kill = 0x0,    // Kill rewards
    pm_reward = 0x1,  // Battle rewards
    pm_shop = 0x3,    // Shop purchases
    // ... more
};
```

## Structure Declarations

The header includes forward declarations for major game structures:
- `CPlayer` - Player character management
- `CMonster` - NPC/monster entities
- `CGameObject` - Base game object class
- `CGuild` - Guild system management
- `CNetWorking` - Network communication
- `CUserDB` - Database operations
- And many more...

## Security Analysis

The server implements multiple security layers:
1. **HackShield Integration** - Commercial anti-cheat system
2. **Movement Validation** - Speed hack detection
3. **Client Verification** - CRC checks and GUID validation
4. **Session Management** - User authentication

## Usage for Reverse Engineering

This analysis can be used for:
- **Game Development Research** - Understanding MMORPG architecture
- **Security Analysis** - Evaluating anti-cheat effectiveness
- **Protocol Analysis** - Understanding client-server communication
- **Educational Purposes** - Learning game server design

## Requirements

- Python 3.6+ (for running the decompiler tools)
- IDA Pro (if you want to generate new header files)

## Limitations

- This analysis is based on type definitions only, not actual function implementations
- Some structures may be incomplete due to IDA's analysis limitations
- Function signatures and implementations would require deeper IDA analysis

## Legal Notice

This analysis is for educational and research purposes only. Ensure you have proper authorization before analyzing any software. Respect intellectual property rights and applicable laws.

## Contributing

If you improve the decompiler or find additional insights, contributions are welcome:
1. Enhance the parsing algorithms
2. Add support for more IDA output formats
3. Improve the categorization of game systems
4. Add function signature extraction

## Future Enhancements

Potential improvements:
- Function signature extraction from vtables
- Memory layout analysis
- Network protocol reconstruction
- Database schema reverse engineering
- Dynamic analysis integration
