#!/usr/bin/env python3
"""
ZoneServer Decompiler
A Python script to parse IDA-generated header files and convert them to C-like code.
"""

import re
import sys
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from pathlib import Path

@dataclass
class StructInfo:
    name: str
    members: List[str]
    is_vtbl: bool = False

@dataclass
class EnumInfo:
    name: str
    values: Dict[str, int]
    base_type: str = "__int32"

@dataclass
class TypedefInfo:
    name: str
    target_type: str

class ZoneServerDecompiler:
    def __init__(self, header_file: str):
        self.header_file = header_file
        self.structs: Dict[str, StructInfo] = {}
        self.enums: Dict[str, EnumInfo] = {}
        self.typedefs: Dict[str, TypedefInfo] = {}
        self.defines: Dict[str, str] = {}
        
    def parse_header(self):
        """Parse the IDA-generated header file"""
        print(f"Parsing header file: {self.header_file}")
        
        with open(self.header_file, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Parse defines
        self._parse_defines(content)
        
        # Parse struct declarations
        self._parse_struct_declarations(content)
        
        # Parse enums
        self._parse_enums(content)
        
        # Parse typedefs
        self._parse_typedefs(content)
        
        print(f"Found {len(self.structs)} structs, {len(self.enums)} enums, {len(self.typedefs)} typedefs")
    
    def _parse_defines(self, content: str):
        """Parse #define statements"""
        define_pattern = r'#define\s+(\w+)\s+(.+)'
        for match in re.finditer(define_pattern, content):
            name, value = match.groups()
            self.defines[name] = value.strip()
    
    def _parse_struct_declarations(self, content: str):
        """Parse struct declarations from the header"""
        # Find struct forward declarations
        struct_pattern = r'struct\s+([^;]+);'
        for match in re.finditer(struct_pattern, content):
            struct_name = match.group(1).strip()
            if struct_name not in self.structs:
                is_vtbl = '_vtbl' in struct_name
                self.structs[struct_name] = StructInfo(
                    name=struct_name,
                    members=[],
                    is_vtbl=is_vtbl
                )
    
    def _parse_enums(self, content: str):
        """Parse enum definitions"""
        # Pattern to match enum definitions
        enum_pattern = r'enum\s+([^:]+)\s*:\s*(\w+)\s*\{([^}]+)\}'
        
        for match in re.finditer(enum_pattern, content, re.DOTALL):
            enum_name = match.group(1).strip()
            base_type = match.group(2).strip()
            enum_body = match.group(3).strip()
            
            values = {}
            for line in enum_body.split(','):
                line = line.strip()
                if '=' in line:
                    name_part, value_part = line.split('=', 1)
                    name = name_part.strip()
                    value_str = value_part.strip()
                    # Convert hex values
                    if value_str.startswith('0x'):
                        value = int(value_str, 16)
                    else:
                        try:
                            value = int(value_str)
                        except ValueError:
                            value = 0
                    values[name] = value
            
            self.enums[enum_name] = EnumInfo(
                name=enum_name,
                values=values,
                base_type=base_type
            )
    
    def _parse_typedefs(self, content: str):
        """Parse typedef statements"""
        typedef_pattern = r'typedef\s+([^;]+);'
        for match in re.finditer(typedef_pattern, content):
            typedef_line = match.group(1).strip()
            parts = typedef_line.split()
            if len(parts) >= 2:
                target_type = ' '.join(parts[:-1])
                name = parts[-1]
                self.typedefs[name] = TypedefInfo(name=name, target_type=target_type)
    
    def generate_c_code(self, output_file: str = "zoneserver_decompiled.c"):
        """Generate C-like code from parsed data"""
        print(f"Generating C code to: {output_file}")
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("/*\n")
            f.write(" * ZoneServer Decompiled Code\n")
            f.write(" * Generated from IDA header file\n")
            f.write(" */\n\n")
            
            # Write includes
            f.write("#include <stdint.h>\n")
            f.write("#include <windows.h>\n\n")
            
            # Write defines
            if self.defines:
                f.write("// Defines\n")
                for name, value in self.defines.items():
                    f.write(f"#define {name} {value}\n")
                f.write("\n")
            
            # Write typedefs
            if self.typedefs:
                f.write("// Type definitions\n")
                for typedef in self.typedefs.values():
                    f.write(f"typedef {typedef.target_type} {typedef.name};\n")
                f.write("\n")
            
            # Write enums
            if self.enums:
                f.write("// Enumerations\n")
                for enum in self.enums.values():
                    self._write_enum(f, enum)
                f.write("\n")
            
            # Write struct declarations
            if self.structs:
                f.write("// Structure declarations\n")
                for struct in self.structs.values():
                    self._write_struct_declaration(f, struct)
                f.write("\n")
    
    def _write_enum(self, f, enum_info: EnumInfo):
        """Write enum definition to file"""
        f.write(f"enum {enum_info.name} : {enum_info.base_type} {{\n")
        for name, value in enum_info.values.items():
            f.write(f"    {name} = 0x{value:X},\n")
        f.write("};\n\n")
    
    def _write_struct_declaration(self, f, struct_info: StructInfo):
        """Write struct declaration to file"""
        if struct_info.is_vtbl:
            f.write(f"// Virtual table for {struct_info.name.replace('_vtbl', '')}\n")
        f.write(f"struct {struct_info.name} {{\n")
        if not struct_info.members:
            f.write("    // Members not defined in header\n")
        else:
            for member in struct_info.members:
                f.write(f"    {member};\n")
        f.write("};\n\n")
    
    def analyze_game_structures(self):
        """Analyze and categorize game-specific structures"""
        categories = {
            'Network': [],
            'Game Objects': [],
            'Database': [],
            'Graphics': [],
            'AI': [],
            'Guild': [],
            'Combat': [],
            'Other': []
        }
        
        for struct_name in self.structs.keys():
            name_lower = struct_name.lower()
            if any(keyword in name_lower for keyword in ['net', 'socket', 'msg', 'packet']):
                categories['Network'].append(struct_name)
            elif any(keyword in name_lower for keyword in ['player', 'monster', 'npc', 'object', 'entity']):
                categories['Game Objects'].append(struct_name)
            elif any(keyword in name_lower for keyword in ['db', 'database', 'query', 'record']):
                categories['Database'].append(struct_name)
            elif any(keyword in name_lower for keyword in ['d3d', 'vertex', 'texture', 'render']):
                categories['Graphics'].append(struct_name)
            elif any(keyword in name_lower for keyword in ['ai', 'path', 'aggro']):
                categories['AI'].append(struct_name)
            elif any(keyword in name_lower for keyword in ['guild', 'party']):
                categories['Guild'].append(struct_name)
            elif any(keyword in name_lower for keyword in ['attack', 'damage', 'skill', 'combat']):
                categories['Combat'].append(struct_name)
            else:
                categories['Other'].append(struct_name)
        
        return categories
    
    def generate_analysis_report(self, output_file: str = "zoneserver_analysis.txt"):
        """Generate an analysis report of the structures"""
        categories = self.analyze_game_structures()

        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("ZoneServer Structure Analysis Report\n")
            f.write("=" * 50 + "\n\n")

            f.write(f"Total Structures: {len(self.structs)}\n")
            f.write(f"Total Enums: {len(self.enums)}\n")
            f.write(f"Total Typedefs: {len(self.typedefs)}\n\n")

            for category, structs in categories.items():
                if structs:
                    f.write(f"{category} ({len(structs)} structures):\n")
                    f.write("-" * 30 + "\n")
                    for struct in sorted(structs):
                        f.write(f"  - {struct}\n")
                    f.write("\n")

            # Add interesting enum analysis
            f.write("Interesting Enums:\n")
            f.write("-" * 20 + "\n")
            for enum_name, enum_info in self.enums.items():
                if len(enum_info.values) > 5:  # Only show enums with many values
                    f.write(f"  {enum_name} ({len(enum_info.values)} values)\n")
            f.write("\n")

    def extract_function_signatures(self, output_file: str = "zoneserver_functions.h"):
        """Extract potential function signatures from vtables"""
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("/*\n")
            f.write(" * Potential Function Signatures from Virtual Tables\n")
            f.write(" */\n\n")

            vtbl_structs = [s for s in self.structs.values() if s.is_vtbl]

            for vtbl in vtbl_structs:
                class_name = vtbl.name.replace('_vtbl', '')
                f.write(f"// Virtual functions for {class_name}\n")
                f.write(f"class {class_name} {{\n")
                f.write("public:\n")
                f.write("    // Virtual function table\n")
                f.write("    virtual ~{}() = 0;\n".format(class_name))
                f.write("    // Add virtual functions here based on analysis\n")
                f.write("};\n\n")

    def generate_ida_script(self, output_file: str = "zoneserver_ida_script.py"):
        """Generate an IDAPython script for further analysis"""
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('"""\n')
            f.write('IDAPython script for ZoneServer analysis\n')
            f.write('Generated by ZoneServer Decompiler\n')
            f.write('"""\n\n')
            f.write('import ida_bytes\n')
            f.write('import ida_name\n')
            f.write('import ida_funcs\n')
            f.write('import ida_struct\n\n')

            f.write('def analyze_zoneserver():\n')
            f.write('    """Main analysis function"""\n')
            f.write('    print("Starting ZoneServer analysis...")\n\n')

            # Add structure creation code
            f.write('    # Create important structures\n')
            important_structs = ['CPlayer', 'CMonster', 'CGameObject', 'CNetWorking']
            for struct_name in important_structs:
                if struct_name in self.structs:
                    f.write(f'    create_struct("{struct_name}")\n')

            f.write('\n')
            f.write('def create_struct(name):\n')
            f.write('    """Create a structure in IDA"""\n')
            f.write('    sid = ida_struct.add_struc(BADADDR, name)\n')
            f.write('    if sid != BADADDR:\n')
            f.write('        print(f"Created structure: {name}")\n')
            f.write('    return sid\n\n')

            f.write('if __name__ == "__main__":\n')
            f.write('    analyze_zoneserver()\n')

def main():
    if len(sys.argv) != 2:
        print("Usage: python zoneserver_decompiler.py <header_file>")
        sys.exit(1)
    
    header_file = sys.argv[1]
    if not Path(header_file).exists():
        print(f"Error: Header file '{header_file}' not found")
        sys.exit(1)
    
    decompiler = ZoneServerDecompiler(header_file)
    decompiler.parse_header()
    decompiler.generate_c_code()
    decompiler.generate_analysis_report()
    
    print("Decompilation complete!")
    print("Generated files:")
    print("  - zoneserver_decompiled.c")
    print("  - zoneserver_analysis.txt")

if __name__ == "__main__":
    main()
